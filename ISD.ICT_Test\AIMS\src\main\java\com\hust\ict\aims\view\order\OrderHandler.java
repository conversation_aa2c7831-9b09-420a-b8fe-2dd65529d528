package com.hust.ict.aims.view.order;

import java.io.IOException;
import java.util.List;

import com.hust.ict.aims.controller.ViewOrderController;
import com.hust.ict.aims.entity.order.Order;
import com.hust.ict.aims.entity.order.OrderMedia;
import com.hust.ict.aims.entity.shipping.DeliveryInfo;
import com.hust.ict.aims.view.BaseScreenHandler;

import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.image.ImageView;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.stage.Stage;

public class OrderHandler extends BaseScreenHandler{
	
	@FXML
    private ImageView aimsImage;

	@FXML
    private TextField searchFieldOrderid;
	
	@FXML
    private TextField searchFieldEmail;

    @FXML
    private Button searchButton;

    @FXML
    private GridPane gridPane;

    @FXML
    private VBox noOrderFoundLabel;

    @FXML
    private Label noOrderFoundMessage;

    @FXML
    private Label orderIdField;

    @FXML
    private Label recipientNameField;
    
    @FXML
    private Label phoneField;
    
    @FXML
    private Label emailField;
    
    @FXML
    private Label addressField;
    
    @FXML
    private Label subtotalField;

    @FXML
    private Label totalField;
    
    @FXML
    private Label deliveryTimeField;    
    
    @FXML
    private Label instructionField;
    
    @FXML
    private Label statusField;

    @FXML
    private TableView<OrderMedia> tableOrderMedia;

    @FXML
    private TableColumn<OrderMedia, Integer> col_id;

    @FXML
    private TableColumn<OrderMedia, String> col_title;

    @FXML
    private TableColumn<OrderMedia, Integer> col_unit_price;

    @FXML
    private TableColumn<OrderMedia, Integer> col_quantity;

    @FXML
    private TableColumn<OrderMedia, Integer> col_price;

    @FXML
    private Button cancelOrderBtn;

    @FXML
    private Label labelDelivery;

    @FXML
    private Label labelInstruction;
    
    public Alert alert;
    
    ClassLoader classLoader = getClass().getClassLoader();
    
    private ViewOrderController viewOrderController = new ViewOrderController();
    
    public OrderHandler(Stage stage, String screenPath) throws IOException {
    	super(stage, screenPath);
    	// TODO Auto-generated constructor stub
    	aimsImage.setOnMouseClicked(e -> {
            homeScreenHandler.show();
        });
    }
    @FXML
    private HBox orderDetailsContainer;

    @FXML
    private void initialize() {
        orderDetailsContainer.setVisible(false);
        
        searchButton.setOnAction(event -> {
            String inputOrder = searchFieldOrderid.getText().trim();
            String inputEmail = searchFieldEmail.getText().trim();

            // Validate input
            if (inputOrder.isEmpty()) {
                alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("Missing Information");
                alert.setHeaderText(null);
                alert.setContentText("Please enter your Order ID to search for your order.");
                alert.showAndWait();
                return;
            }

            if (inputEmail.isEmpty()) {
                alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("Missing Information");
                alert.setHeaderText(null);
                alert.setContentText("Please enter your email address to verify your order.");
                alert.showAndWait();
                return;
            }

            // Validate Order ID is numeric
            try {
                Integer.parseInt(inputOrder);
            } catch (NumberFormatException e) {
                alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("Invalid Order ID");
                alert.setHeaderText(null);
                alert.setContentText("Order ID must be a number. Please check your Order ID and try again.");
                alert.showAndWait();
                return;
            }

            // Search for order
            List<OrderMedia> mediaInOrder;
            try {
                Order orderInfo = viewOrderController.getOrderById(Integer.parseInt(inputOrder), inputEmail);
                // Display order details container and fill information
                orderDetailsContainer.setVisible(true);
                noOrderFoundLabel.setVisible(false);
                mediaInOrder = orderInfo.getLstOrderMedia();

                fillMediaInOrder(mediaInOrder);
                fillOrderInformation(orderInfo);

                // Show success message
                alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("Order Found");
                alert.setHeaderText(null);
                alert.setContentText("Order found successfully! Your order details are displayed below.");
                alert.showAndWait();

            } catch(Exception e) {
                // Display message that order was not found
                System.err.println(e.getMessage());
                noOrderFoundMessage.setText("No order found with ID '" + inputOrder + "' for email '" + inputEmail + "'. Please check your information and try again.");
                noOrderFoundLabel.setVisible(true);
                orderDetailsContainer.setVisible(false);
            }
        });
    }
    private void fillOrderInformation(Order orderInfo) {
    	System.out.println("thong tin order: " + orderInfo.getId());
		orderIdField.setText(String.valueOf(orderInfo.getId()));
		subtotalField.setText(String.valueOf(orderInfo.getSubtotal()));
		statusField.setText(orderInfo.getStatus().toString());
		totalField.setText(String.valueOf((int) (orderInfo.getSubtotal()*1.1) + orderInfo.getShippingFees()));
		DeliveryInfo delivery = orderInfo.getDeliveryInfo();
		recipientNameField.setText(delivery.getName());
		phoneField.setText(delivery.getPhone());
		emailField.setText(delivery.getEmail());
		addressField.setText(delivery.getAddress());

		// TODO: FIX THIS with rushorder
//        if(orderInfo.get(7).equals("Khong co") && orderInfo.get(8).equals("Khong co")) {
//            deliveryTimeField.setVisible(false);
//            instructionField.setVisible(false);
//            labelDelivery.setVisible(false);
//            labelInstruction.setVisible(false);
//        } else {
//            deliveryTimeField.setText(orderInfo.get(7));
//            instructionField.setText(orderInfo.get(8));
//        }
		// TODO: FIX THIS TOO
        // totalField.setText(orderInfo.getT);
    	
    }

    private void fillMediaInOrder(List<OrderMedia> mediaInOrder){
        col_id.setCellValueFactory(cellData -> new SimpleIntegerProperty(cellData.getValue().getMedia().getMediaId()).asObject());
        col_title.setCellValueFactory(cellData -> new SimpleStringProperty(cellData.getValue().getMedia().getTitle()));
        col_unit_price.setCellValueFactory(cellData -> new SimpleIntegerProperty(cellData.getValue().getMedia().getMediaId()).asObject());
        col_quantity.setCellValueFactory(cellData -> new SimpleIntegerProperty(cellData.getValue().getMedia().getMediaId()).asObject());
        col_price.setCellValueFactory(cellData -> new SimpleIntegerProperty(cellData.getValue().getMedia().getMediaId()).asObject());

        ObservableList<OrderMedia> observableData = FXCollections.observableArrayList(mediaInOrder);
        tableOrderMedia.setItems(observableData);
    }
}