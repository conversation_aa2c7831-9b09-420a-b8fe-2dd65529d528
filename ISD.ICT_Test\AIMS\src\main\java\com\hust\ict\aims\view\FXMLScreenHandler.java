package com.hust.ict.aims.view;

import java.io.File;
import java.io.IOException;

import javafx.fxml.FXMLLoader;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.AnchorPane;

public class FXMLScreenHandler {
    protected FXMLLoader loader;
    protected AnchorPane content;

    public FXMLScreenHandler(String screenPath) throws IOException {
        System.out.println("Loading FXML from path: " + screenPath);
        java.net.URL resourceUrl = getClass().getResource(screenPath);
        System.out.println("Resource URL: " + resourceUrl);

        if (resourceUrl == null) {
            throw new IOException("Cannot find FXML resource: " + screenPath);
        }

        this.loader = new FXMLLoader(resourceUrl);
        // Set this class as the controller
        this.loader.setController(this);
        this.content = (AnchorPane) loader.load();
    }

    public AnchorPane getContent() {
        return this.content;
    }

    public FXMLLoader getLoader() {
        return this.loader;
    }

    public void setImage(ImageView imv, String path){
        File file = new File(path);
        Image img = new Image(file.toURI().toString());
        imv.setImage(img);
    }
}
